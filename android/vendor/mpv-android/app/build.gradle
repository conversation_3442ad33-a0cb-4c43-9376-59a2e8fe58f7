apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

ext.abiCodes = ["armeabi-v7a": 1, "arm64-v8a": 2, "x86":3, "x86_64":4]
ext.universalBase = 8000

android {
    namespace 'is.xyz.mpv'
    compileSdk 35

    defaultConfig {
        minSdkVersion 21
        targetSdk 35

        versionCode 41
        versionName "2025-04-21-release"

        vectorDrawables.useSupportLibrary = true
    }

    flavorDimensions "default"
    productFlavors {
        "default" {
            isDefault true
        }
        api29 {
            targetSdkVersion 29
            versionNameSuffix "-oldapi"
        }
    }

    buildFeatures {
        viewBinding = true
        buildConfig = true
    }

    splits {
        abi {
            enable true
            reset()
            // include only the ABIs that were actually built
            project.ext.abiCodes.each { abi, _ ->
                if (new File(project.projectDir, "src/main/jniLibs/${abi}").exists())
                    include(abi)
            }
            universalApk true // build an APK with all ABIs too
        }
    }

    // https://youtrack.jetbrains.com/issue/KT-55947
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile) {
        kotlinOptions.jvmTarget = JavaVersion.VERSION_11.toString()
    }
}

// Map versionCode so each ABI gets a different one
// e.g. x86 with version 21 gets a versionCode of 3021
import com.android.build.VariantOutput
android.applicationVariants.all { variant ->
    variant.outputs.each { output ->
        def base = project.ext.abiCodes.get(output.getFilter(VariantOutput.ABI))
        // universal APK just gets a constant added to it
        if (base != null)
            output.versionCodeOverride = base * 1000 + variant.versionCode
        else
            output.versionCodeOverride = universalBase + variant.versionCode
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation 'androidx.appcompat:appcompat:1.7.1'
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'androidx.media:media:1.7.0'
    implementation 'androidx.preference:preference-ktx:1.2.1'
    implementation 'com.google.android.material:material:1.12.0'
}
