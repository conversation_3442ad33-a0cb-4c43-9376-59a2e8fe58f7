<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:installLocation="auto">

    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature android:glEsVersion="0x00020000" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />

    <application
        android:allowBackup="true"
        android:resizeableActivity="true"
        android:requestLegacyExternalStorage="true"
        android:preserveLegacyExternalStorage="true"
        android:icon="@mipmap/mpv_launcher_icon"
        android:roundIcon="@mipmap/mpv_launcher_icon"
        android:banner="@mipmap/ic_banner"
        android:label="@string/mpv_activity"
        android:appCategory="video"
        android:localeConfig="@xml/locales_config">

        <!-- Multi-window support on some devices -->
        <meta-data android:name="com.samsung.android.sdk.multiwindow.enable"
            android:value="true" />
        <meta-data android:name="com.lge.support.SPLIT_WINDOW"
            android:value="true" />

        <activity
            android:name=".MPVActivity"
            android:configChanges="keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:supportsPictureInPicture="true"
            android:launchMode="singleTask"
            android:alwaysRetainTaskState="true"
            android:autoRemoveFromRecents="true"
            android:theme="@style/AppTheme"
            android:exported="true">
            <intent-filter> <!-- Media protocols -->
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="rtmp" />
                <data android:scheme="rtmps" />
                <data android:scheme="rtp" />
                <data android:scheme="rtsp" />
                <data android:scheme="mms" />
                <data android:scheme="mmst" />
                <data android:scheme="mmsh" />
                <data android:scheme="tcp" />
                <data android:scheme="udp" />
            </intent-filter>
            <intent-filter> <!-- Local files with MIME type (e.g. Google Drive) -->
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="content" />
                <data android:scheme="file" />
                <data android:mimeType="video/*" />
                <data android:mimeType="audio/*" />
            </intent-filter>
            <intent-filter> <!-- HTTP(S) with MIME type (#43) -->
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:scheme="https" />
                <!-- no android:host since Android 12 doesn't like it, see #497 #514 -->
                <data android:mimeType="video/*" />
                <data android:mimeType="audio/*" />
                <data android:mimeType="application/vnd.apple.mpegurl" />
            </intent-filter>
            <intent-filter> <!-- HTTP(S) with extension (e.g. Chrome) -->
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="*" />
                <!-- the duplicate patterns below work around an Android bug: -->
                <!-- http://stackoverflow.com/questions/3400072/#answer-8599921 -->
                <data android:pathPattern=".*\\.mkv" />
                <data android:pathPattern=".*\\..*\\.mkv" />
                <data android:pathPattern=".*\\..*\\..*\\.mkv" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.mkv" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mkv" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.mkv" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.mkv" />

                <data android:pathPattern=".*\\.mp4" />
                <data android:pathPattern=".*\\..*\\.mp4" />
                <data android:pathPattern=".*\\..*\\..*\\.mp4" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.mp4" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mp4" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.mp4" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.mp4" />

                <data android:pathPattern=".*\\.webm" />
                <data android:pathPattern=".*\\..*\\.webm" />
                <data android:pathPattern=".*\\..*\\..*\\.webm" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.webm" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.webm" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.webm" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.webm" />

                <data android:pathPattern=".*\\.avi" />
                <data android:pathPattern=".*\\..*\\.avi" />
                <data android:pathPattern=".*\\..*\\..*\\.avi" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.avi" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.avi" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.avi" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.avi" />

                <data android:pathPattern=".*\\.mov" />
                <data android:pathPattern=".*\\..*\\.mov" />
                <data android:pathPattern=".*\\..*\\..*\\.mov" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.mov" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mov" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.mov" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.mov" />

                <data android:pathPattern=".*\\.m4v" />
                <data android:pathPattern=".*\\..*\\.m4v" />
                <data android:pathPattern=".*\\..*\\..*\\.m4v" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.m4v" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.m4v" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.m4v" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.m4v" />

                <data android:pathPattern=".*\\.flac" />
                <data android:pathPattern=".*\\..*\\.flac" />
                <data android:pathPattern=".*\\..*\\..*\\.flac" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.flac" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.flac" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.flac" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.flac" />

                <data android:pathPattern=".*\\.mp3" />
                <data android:pathPattern=".*\\..*\\.mp3" />
                <data android:pathPattern=".*\\..*\\..*\\.mp3" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.mp3" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.mp3" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.mp3" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.mp3" />

                <data android:pathPattern=".*\\.ogg" />
                <data android:pathPattern=".*\\..*\\.ogg" />
                <data android:pathPattern=".*\\..*\\..*\\.ogg" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.ogg" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.ogg" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.ogg" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.ogg" />

                <data android:pathPattern=".*\\.m3u8*" />
                <data android:pathPattern=".*\\..*\\.m3u8*" />
                <data android:pathPattern=".*\\..*\\..*\\.m3u8*" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.m3u8*" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.m3u8*" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.m3u8*" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.m3u8*" />
            </intent-filter>
            <intent-filter android:label="@string/label_share_intent"> <!-- Share action -->
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/plain" />
            </intent-filter>
        </activity>

        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|screenSize"
            android:theme="@style/FilePickerTheme"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".FilePickerActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="behind"
            android:theme="@style/FilePickerThemeSpecial"
            android:exported="false" />

        <activity
            android:name=".preferences.PreferenceActivity"
            android:theme="@style/AppTheme.Preference"
            android:parentActivityName=".MainActivity"
            android:launchMode="singleTop"
            android:label="@string/title_activity_settings" />

        <activity
            android:name=".preferences.AboutActivity"
            android:theme="@style/AppTheme.Preference"
            android:parentActivityName=".preferences.PreferenceActivity"
            android:launchMode="singleTop"
            android:label="@string/pref_header_about_mpv" />

        <service
            android:name=".BackgroundPlaybackService"
            android:foregroundServiceType="mediaPlayback"
            android:exported="false" />

        <receiver android:name=".NotificationButtonReceiver" android:exported="false">
            <intent-filter>
                <action android:name="is.xyz.mpv.PLAY_PAUSE" />
                <action android:name="is.xyz.mpv.ACTION_PREV" />
                <action android:name="is.xyz.mpv.ACTION_NEXT" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
