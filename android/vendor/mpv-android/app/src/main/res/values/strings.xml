<?xml version="1.0" encoding="utf-8"?>
<!-- This file contains resource definitions for displayed strings, allowing
     them to be changed based on the locale and options. -->
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">
    <!-- Simple strings. -->
    <string name="mpv_activity">mpv</string>
    <string name="label_share_intent">Play in mpv</string>
    <string name="title_activity_settings">Settings</string>
    <string name="dialog_ok">OK</string>
    <string name="dialog_cancel">Cancel</string>
    <string name="dialog_save">Save</string>
    <string name="dialog_yes">Yes</string>
    <string name="dialog_no">No</string>
    <string name="dialog_prev">Prev</string>
    <string name="dialog_next">Next</string>
    <string name="dialog_reset">Reset</string>
    <string name="track_audio">Audio</string>
    <string name="track_subs">Subs</string> <!-- short for "Subtitles" -->
    <string name="track_off">Off</string>
    <string name="track_primary">Primary</string>
    <string name="track_secondary">Secondary</string>
    <string name="btn_play">Play</string>
    <string name="btn_pause">Pause</string>
    <string name="btn_plus" translatable="false">+</string>
    <string name="btn_minus" translatable="false">-</string>
    <string name="ui_volume">Volume: %d%%</string>
    <string name="ui_brightness">Brightness: %d%%</string>
    <string name="ui_seek_distance">%1$s\n[%2$s]</string>
    <string name="ui_fps">%.3f FPS</string>
    <string name="ui_speed">%.2fx</string>
    <string name="ui_chapter_fallback">Chapter %1$d (%2$s)</string>
    <string name="ui_chapter">%1$s (%2$s)</string>
    <string name="ui_track">#%d</string>
    <string name="ui_track_text">#%1$d: %2$s</string>
    <string name="ui_track_title_lang">#%1$d: %2$s (%3$s)</string>
    <string name="format_fixed_number" translatable="false">%.0f</string>
    <string name="format_seconds">%.1f s</string>

    <!-- Less simple strings -->
    <string name="title_speed_dialog">Playback speed</string>
    <string name="error_no_file">No file given, exiting</string>
    <string name="notice_file_appended">File was appended to playlist</string>
    <string name="exit_warning_playlist">%d playlist items remaining.\nDo you really want to exit?</string>
    <string name="contrast">Contrast</string>
    <string name="video_brightness">Video Brightness</string>
    <string name="gamma">Gamma</string>
    <string name="saturation">Saturation</string>
    <string name="aspect_ratio">Aspect ratio</string>
    <string name="audio_delay">Audio delay</string>
    <string name="sub_delay">Subtitle delay</string>
    <string name="notice_show_all_files">Showing all files</string>
    <string name="notice_show_media_files">Showing media files only</string>
    <string name="option_remember_choice">Remember choice on next startup</string>
    <string name="uri_invalid_protocol">Invalid protocol</string>

    <!-- Action buttons -->
    <string name="switch_orientation">Switch Orientation</string>
    <string name="open_external_audio">Open external audio…</string>
    <string name="open_external_sub">Open external subtitle…</string>
    <string name="advanced_menu">Advanced…</string>
    <string name="resume_bg_playback">Play in background</string>
    <string name="toggle_stats">Toggle stats</string>
    <string name="chapter_button">Chapter</string>
    <string name="sub_seek_button">Seek by subtitle</string>
    <string name="file_picker_old">File Picker\n(legacy)</string>
    <string name="action_settings">Settings</string>
    <string name="action_playlist">Playlist</string>
    <string name="action_open_url">Open URL</string>
    <string name="action_open_doc">Open Document</string>
    <string name="action_open_doc_tree">Open Document Tree</string>
    <string name="action_pick_file">Pick File</string>
    <string name="action_pick_file_old">Pick File (legacy)</string>
    <string name="action_external_storage">External storage</string>
    <string name="action_toggle_filter">Toggle Filter</string>

    <!-- Strings related to Settings -->
    <string name="pref_header_general">General</string>
    <string name="pref_header_general_summary"></string>

    <string name="pref_header_video">Video</string>
    <string name="pref_header_video_summary"></string>

    <string name="pref_header_ui">User Interface</string>
    <string name="pref_header_ui_summary"></string>

    <string name="pref_header_gestures">Touch gestures</string>
    <string name="pref_header_gestures_summary"></string>

    <string name="pref_header_developer">Developer</string>
    <string name="pref_header_developer_summary"></string>

    <string name="pref_header_advanced">Advanced</string>
    <string name="pref_header_advanced_summary"></string>

    <string name="pref_header_about_mpv">About mpv</string>
    <string name="pref_header_about_mpv_summary"></string>

    <string name="pref_default_file_manager_path_title">Default File Manager Path</string>
    <string name="pref_default_file_manager_path_summary" translatable="false">%s</string>

    <string name="pref_material_you_theming_title">Material You</string>
    <string name="pref_material_you_theming_summary">Use dynamic colors</string>

    <string name="pref_default_audio_language_title">Default Audio Language</string>
    <string name="pref_default_audio_language_message">Audio language(s) to be selected by default when playing a video with multiple audio streams.\nTwo- or three-letter languages codes work. Multiple values can be delimited by comma.</string>
    <string name="pref_default_audio_language_summary" translatable="false">%s</string>

    <string name="pref_default_subtitle_language_title">Default Subtitle Language</string>
    <string name="pref_default_subtitle_language_message">Subtitle language(s) to be selected by default when playing a video with multiple subtitles.\nTwo- or three-letter languages codes work. Multiple values can be delimited by comma.</string>
    <string name="pref_default_subtitle_language_summary" translatable="false">%s</string>

    <string name="pref_hardware_decoding_title">Hardware decoding</string>
    <string name="pref_hardware_decoding_summary">Attempt hardware decoding before falling back to software. This is generally more efficient.</string>

    <string name="pref_background_play_title">Background playback</string>
    <string name="pref_background_play_summary">Decide when playback should be automatically resumed background.</string>
    <string name="pref_background_play_default" translatable="false">never</string>

    <string name="pref_save_position_title">Save position on quit</string>
    <string name="pref_save_position_summary">Remember current playback position on exit. When the same file is played again mpv will seek to the previous position.</string>

    <string name="pref_auto_rotation_title">Screen orientation</string>
    <string name="pref_auto_rotation_summary">Decide which orientation mpv will play landscape or portrait videos in.</string>
    <string name="pref_auto_rotation_default" translatable="false">auto</string>

    <string name="pref_display_media_title_title">Display media title (videos)</string>
    <string name="pref_display_media_title_summary">Display the title or filename of videos above the controls. Audio files will always display their metadata.</string>

    <string name="pref_bottom_controls_title">Show controls at bottom of screen</string>

    <string name="pref_no_ui_pause_title">Continue playback during UI popups</string>
    <string name="pref_no_ui_pause_summary">Continue playback while popups are open. (e.g. during the playlist dialog)</string>
    <string name="pref_no_ui_pause_default" translatable="false">audio-only</string>

    <string name="pref_playlist_exit_warning_title">Playlist exit confirmation</string>
    <string name="pref_playlist_exit_warning_summary">Show a confirmation dialog before exiting when a playlist is loaded.</string>

    <string name="pref_seek_gesture_smooth_title">Smoother seeking</string>

    <string name="pref_gesture_horiz_title">Horizontal drag</string>
    <string name="pref_gesture_horiz_default" translatable="false">seek</string>

    <string name="pref_gesture_vert_left_title">Vertical drag (left side)</string>
    <string name="pref_gesture_vert_left_default" translatable="false">bright</string>

    <string name="pref_gesture_vert_right_title">Vertical drag (right side)</string>
    <string name="pref_gesture_vert_right_default" translatable="false">volume</string>

    <string name="pref_gesture_tap_left_title">Double tap (left)</string>
    <string name="pref_gesture_tap_left_default" translatable="false">none</string>

    <string name="pref_gesture_tap_center_title">Double tap (center)</string>
    <string name="pref_gesture_tap_center_default" translatable="false">none</string>

    <string name="pref_gesture_tap_right_title">Double tap (right)</string>
    <string name="pref_gesture_tap_right_default" translatable="false">none</string>

    <string name="pref_gesture_custom_helptext">When a tap gesture is set to \"Custom\" it can be bound to any command by editing input.conf. The key codes are <b>0x10001</b> (left), <b>0x10002</b> (center), <b>0x10003</b> (right).\nFor example you could bind to seeking by 6s:\n\t\t0x10003 no-osd seek 6</string>

    <string name="pref_video_upscale_title">Upscaling filter</string>

    <string name="pref_video_downscale_title">Downscaling filter</string>

    <string name="pref_video_debanding_title">Debanding</string>
    <string name="pref_video_debanding_summary">Select video debanding mode.</string>

    <string name="pref_video_interpolation_title">Interpolation</string>
    <string name="pref_video_interpolation_message">Reduce judder caused by mismatched video FPS and display refresh rate.</string>
    <string name="pref_video_interpolation_sync_default" translatable="false">audio</string>

    <string name="pref_video_tscale_title">Temporal interpolation filter</string>
    <string name="pref_video_tscale_summary">Select the filter used for interpolating the temporal axis (frames).</string>
    <string name="pref_video_tscale_message">These settings only take effect if interpolation is enabled above.</string>

    <string name="pref_video_fastdecode_title">Low-quality video decoding</string>
    <string name="pref_video_fastdecode_summary">Trades quality for performance and thus fluent playback.\n<b>REDUCES QUALITY A LOT</b></string>

    <string name="pref_ignore_audio_focus_title">Ignore Audio focus</string>
    <string name="pref_ignore_audio_focus_summary">Do not pause playback or reduce volume when other applications play audio at the same time.</string>

    <string name="pref_stats_mode_title">Show stats</string>
    <string name="pref_stats_mode_summary">Choose which statistics are displayed</string>

    <string name="pref_gpu_debug_title">Enable OpenGL debugging</string>

    <string name="pref_gpu_next_title">Use gpu-next</string>
    <string name="pref_gpu_next_summary">Use new video rendering backend, based on libplacebo.</string>

    <string name="pref_version_info">Version information</string>

    <string name="pref_edit_mpv_conf">Edit mpv.conf</string>
    <string name="pref_edit_mpv_conf_message">You can directly edit mpv\'s configuration here.</string>

    <string name="pref_edit_input_conf">Edit input.conf</string>
    <string name="pref_edit_input_conf_message">You can edit input.conf here, this is mainly useful for TV remotes and keyboards.\n\nImportant: mpv-android has some hardcoded keys which can\'t be redefined here, e.g. \'j\' to cycle subtitles.</string>

    <!-- Strings used in Settings dialog preferences -->
    <string name="scaler_filter">Filter function</string>
    <string name="scaler_param1">Param 1</string>
    <string name="scaler_param2">Param 2</string>
    <string name="interpolation_switch">Enable Interpolation</string>
    <string name="interpolation_video_sync">Video sync</string>

    <!-- File picker -->
    <string name="nnf_name">Name</string>
    <string name="nnf_permission_external_write_denied">Permission to access file system denied</string>
</resources>
