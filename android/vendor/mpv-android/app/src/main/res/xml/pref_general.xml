<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <EditTextPreference
        android:defaultValue="/sdcard"
        android:key="default_file_manager_path"
        app:useSimpleSummaryProvider="true"
        app:iconSpaceReserved="false"
        android:hint=""
        android:title="@string/pref_default_file_manager_path_title" />

    <EditTextPreference
        android:defaultValue="eng"
        android:key="default_audio_language"
        app:iconSpaceReserved="false"
        app:useSimpleSummaryProvider="true"
        android:dialogMessage="@string/pref_default_audio_language_message"
        android:title="@string/pref_default_audio_language_title" />

    <EditTextPreference
        android:defaultValue="eng"
        app:iconSpaceReserved="false"
        android:key="default_subtitle_language"
        app:useSimpleSummaryProvider="true"
        android:dialogMessage="@string/pref_default_subtitle_language_message"
        android:title="@string/pref_default_subtitle_language_title" />

    <SwitchPreferenceCompat
        android:defaultValue="false"
        android:key="material_you_theming"
        android:summary="@string/pref_material_you_theming_summary"
        android:title="@string/pref_material_you_theming_title"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:defaultValue="true"
        app:iconSpaceReserved="false"
        android:key="hardware_decoding"
        android:summary="@string/pref_hardware_decoding_summary"
        android:title="@string/pref_hardware_decoding_title" />

    <ListPreference
        android:defaultValue="@string/pref_background_play_default"
        app:iconSpaceReserved="false"
        android:key="background_play"
        android:summary="@string/pref_background_play_summary"
        android:entries="@array/background_play_entries"
        android:entryValues="@array/background_play_values"
        android:title="@string/pref_background_play_title" />

    <SwitchPreferenceCompat
        android:defaultValue="false"
        app:iconSpaceReserved="false"
        android:key="save_position"
        android:summary="@string/pref_save_position_summary"
        android:title="@string/pref_save_position_title" />

</PreferenceScreen>
