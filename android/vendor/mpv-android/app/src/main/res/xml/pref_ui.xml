<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ListPreference
        android:defaultValue="@string/pref_auto_rotation_default"
        app:iconSpaceReserved="false"
        android:key="auto_rotation"
        android:summary="@string/pref_auto_rotation_summary"
        android:entries="@array/auto_rotation_entries"
        android:entryValues="@array/auto_rotation_values"
        android:title="@string/pref_auto_rotation_title" />

    <SwitchPreferenceCompat
        app:iconSpaceReserved="false"
        android:defaultValue="false"
        android:key="display_media_title"
        android:summary="@string/pref_display_media_title_summary"
        android:title="@string/pref_display_media_title_title" />

    <SwitchPreferenceCompat
        app:iconSpaceReserved="false"
        android:defaultValue="true"
        android:key="bottom_controls"
        android:summary=""
        android:title="@string/pref_bottom_controls_title" />

    <ListPreference
        android:defaultValue="@string/pref_no_ui_pause_default"
        app:iconSpaceReserved="false"
        android:key="no_ui_pause"
        android:summary="@string/pref_no_ui_pause_summary"
        android:entries="@array/background_play_entries"
        android:entryValues="@array/background_play_values"
        android:title="@string/pref_no_ui_pause_title" />

    <SwitchPreferenceCompat
        android:defaultValue="true"
        app:iconSpaceReserved="false"
        android:key="playlist_exit_warning"
        android:summary="@string/pref_playlist_exit_warning_summary"
        android:title="@string/pref_playlist_exit_warning_title" />

</PreferenceScreen>
