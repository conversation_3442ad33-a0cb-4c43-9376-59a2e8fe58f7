<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingHorizontal="24dp"
    android:paddingTop="8dp">

    <com.google.android.material.textfield.TextInputLayout
        style="?textInputOutlinedExposedDropdownMenuStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <AutoCompleteTextView
            android:id="@+id/scaler"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/scaler_filter"
            android:inputType="none" />

    </com.google.android.material.textfield.TextInputLayout>


    <com.google.android.material.textfield.TextInputLayout
        style="?textInputOutlinedStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <EditText
            android:id="@+id/param1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:autofillHints=""
            android:hint="@string/scaler_param1"
            android:inputType="numberSigned|numberDecimal" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        style="?textInputOutlinedStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <EditText
            android:id="@+id/param2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:autofillHints=""
            android:hint="@string/scaler_param2"
            android:inputType="numberSigned|numberDecimal" />
    </com.google.android.material.textfield.TextInputLayout>
</LinearLayout>
