<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingHorizontal="24dp"
    android:paddingTop="16dp">


    <com.google.android.material.materialswitch.MaterialSwitch
        android:id="@+id/switch1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/interpolation_switch" />


    <com.google.android.material.textfield.TextInputLayout
        style="?textInputOutlinedExposedDropdownMenuStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp">

        <AutoCompleteTextView
            android:id="@+id/video_sync"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/interpolation_video_sync"
            android:inputType="none" />

    </com.google.android.material.textfield.TextInputLayout>

</LinearLayout>
