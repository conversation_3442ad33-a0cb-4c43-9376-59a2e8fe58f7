<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ This Source Code Form is subject to the terms of the Mozilla Public
  ~ License, v. 2.0. If a copy of the MPL was not distributed with this
  ~ file, You can obtain one at http://mozilla.org/MPL/2.0/.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nnf_item_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?selectableItemBackground"
    android:focusable="true"
    android:minHeight="?listPreferredItemHeight"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/item_icon"
        android:layout_width="?listPreferredItemHeight"
        android:layout_height="?listPreferredItemHeight"
        android:layout_gravity="center_vertical"
        android:adjustViewBounds="true"
        android:scaleType="center"
        app:srcCompat="@drawable/nnf_ic_folder_black_48dp"
        app:tint="?attr/colorAccent"
        android:visibility="visible"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@android:id/text1"
        android:textAppearance="@style/TextAppearance.AppCompat.Large"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:minHeight="?listPreferredItemHeight"
        android:gravity="center_vertical"
        android:padding="8dp"
        android:text="@string/nnf_name" />

</LinearLayout>
