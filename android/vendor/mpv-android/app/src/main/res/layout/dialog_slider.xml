<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:text=""
        android:textAppearance="@style/TextAppearance.AppCompat.Body1"
        android:textStyle="bold"
        tools:text="123" />

    <Button
        android:id="@+id/resetBtn"
        style="@style/Widget.AppCompat.Button.Borderless.Colored"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_gravity="end"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:text="@string/dialog_reset" />

    <SeekBar
        android:id="@+id/seekBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/resetBtn" />


</RelativeLayout>
