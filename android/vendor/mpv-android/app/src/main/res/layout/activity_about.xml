<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    tools:context=".preferences.AboutActivity">

    <ImageView
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginVertical="16dp"
        android:contentDescription="@string/mpv_activity"
        app:srcCompat="@drawable/mpv_logo" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="16dp">

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/logs"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="monospace"
                android:padding="12dp"
                android:textIsSelectable="true"
                tools:text="Never gonna give mpv up" />

        </HorizontalScrollView>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>
