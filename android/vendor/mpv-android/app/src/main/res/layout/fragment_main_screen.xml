<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="24dp">

    <ImageView
        android:id="@+id/logo"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="3"
        android:contentDescription="@string/mpv_activity"
        app:srcCompat="@drawable/mpv_logo" />

    <!-- FIXME this could look better -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:gravity="center"
        android:orientation="horizontal">

        <Button
            android:id="@+id/docBtn"
            style="@style/Widget.AppCompat.Button.Colored"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:drawableTop="@drawable/ic_file_open_48dp"
            android:text="@string/action_open_doc_tree"
            android:textColor="@android:color/primary_text_dark" />

        <Button
            android:id="@+id/urlBtn"
            style="@style/Widget.AppCompat.Button.Colored"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:drawableTop="@drawable/ic_link_48dp"
            android:text="@string/action_open_url"
            android:textColor="@android:color/primary_text_dark" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:gravity="center"
        android:orientation="horizontal">

        <Button
            android:id="@+id/filepickerBtn"
            style="@style/Widget.AppCompat.Button.Colored"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:drawableTop="@drawable/ic_folder_white_48dp"
            android:text="@string/file_picker_old"
            android:textColor="@android:color/primary_text_dark" />

        <Button
            android:id="@+id/settingsBtn"
            style="@style/Widget.AppCompat.Button.Colored"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:drawableTop="@drawable/ic_settings_black_48dp"
            android:text="@string/action_settings"
            android:textColor="@android:color/primary_text_dark" />

    </LinearLayout>

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/switch1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:text="@string/option_remember_choice"
        app:showText="false" />

</LinearLayout>
