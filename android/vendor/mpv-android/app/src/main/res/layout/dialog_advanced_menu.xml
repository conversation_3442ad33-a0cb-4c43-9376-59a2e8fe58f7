<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:orientation="vertical" android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TableRow
            android:id="@+id/rowVideo1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/contrastBtn"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/contrast"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/brightnessBtn"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/video_brightness"
                android:textAllCaps="false" />

        </TableRow>

        <TableRow
            android:id="@+id/rowVideo2"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <Button
                android:id="@+id/gammaBtn"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/gamma"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/saturationBtn"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/saturation"
                android:textAllCaps="false" />
        </TableRow>

        <TableRow
            android:id="@+id/rowSubSeek"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/subSeekBtn"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="4"
                android:text="@string/sub_seek_button"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/subSeekPrev"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/alpha_darken"
                android:text="@string/dialog_prev"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/subSeekNext"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/alpha_darken"
                android:text="@string/dialog_next"
                android:textAllCaps="false" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/statsBtn"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="4"
                android:text="@string/toggle_stats"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/statsBtn1"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/alpha_darken"
                android:text="1"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/statsBtn2"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/alpha_darken"
                android:text="2"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/statsBtn3"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/alpha_darken"
                android:text="3"
                android:textAllCaps="false" />

        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <Button
                android:id="@+id/audioDelayBtn"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/audio_delay"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/subDelayBtn"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/sub_delay"
                android:textAllCaps="false" />

        </TableRow>

        <Button
            android:id="@+id/aspectBtn"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/aspect_ratio"
            android:textAllCaps="false" />

    </LinearLayout>

</ScrollView>
