<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="64dp"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <TextView
        android:id="@android:id/message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textAppearance="@style/TextAppearance.AppCompat.Large"
        tools:text="Heading" />

    <Button
        android:id="@+id/fileBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:paddingVertical="16dp"
        android:text="@string/action_pick_file_old" />

    <Button
        android:id="@+id/urlBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:paddingVertical="16dp"
        android:text="@string/action_open_url" />

    <Button
        android:id="@+id/docBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:paddingVertical="16dp"
        android:text="@string/action_open_doc" />

</LinearLayout>
