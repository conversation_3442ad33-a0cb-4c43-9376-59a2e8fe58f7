<?xml version="1.0" encoding="utf-8"?>

<!--
  This program is free software: you can redistribute it and/or modify
  it under the terms of the GNU Lesser General Public License as published by
  the Free Software Foundation, either version 3 of the License, or
  (at your option) any later version.

  This program is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU Lesser General Public License for more details.

  You should have received a copy of the GNU Lesser General Public License
  along with this program.  If not, see <http://www.gnu.org/licenses/>.
  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nnf_item_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?selectableItemBackground"
    android:focusable="true"
    android:minHeight="?android:listPreferredItemHeight"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/item_icon"
        android:layout_width="?listPreferredItemHeight"
        android:layout_height="?listPreferredItemHeight"
        android:layout_gravity="center_vertical"
        android:adjustViewBounds="true"
        android:scaleType="center"
        app:srcCompat="@drawable/nnf_ic_folder_black_48dp"
        app:tint="?attr/colorAccent"
        android:visibility="visible"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@android:id/text1"
        android:textAppearance="@style/TextAppearance.AppCompat.Large"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:minHeight="?listPreferredItemHeight"
        android:gravity="center_vertical"
        android:padding="8dp"
        android:text="@string/nnf_name" />

</LinearLayout>
