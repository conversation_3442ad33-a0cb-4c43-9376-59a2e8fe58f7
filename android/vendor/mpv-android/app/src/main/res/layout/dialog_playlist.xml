<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="16dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/action_playlist"
            android:textAppearance="@style/TextAppearance.AppCompat.Title" />

        <ImageButton
            android:id="@+id/shuffleBtn"
            style="?buttonBarStyle"
            android:background="?selectableItemBackground"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="8dp"
            android:src="@drawable/ic_shuffle_24dp" />

        <ImageButton
            android:id="@+id/repeatBtn"
            style="?buttonBarStyle"
            android:background="?selectableItemBackground"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="8dp"
            android:src="@drawable/ic_repeat_24dp" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="8dp"
        app:layoutManager="LinearLayoutManager"
        tools:listitem="@layout/dialog_playlist_item">

    </androidx.recyclerview.widget.RecyclerView>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?android:attr/listDivider" />

    <TableRow
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <Button
            android:id="@+id/fileBtn"
            style="@style/Widget.AppCompat.Button.Borderless.Colored"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/action_pick_file" />

        <Button
            android:id="@+id/urlBtn"
            style="@style/Widget.AppCompat.Button.Borderless.Colored"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/action_open_url" />

    </TableRow>

</LinearLayout>
