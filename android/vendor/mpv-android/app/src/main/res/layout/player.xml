<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <is.xyz.mpv.MPVView
        android:id="@+id/player"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- so that insets can be applied as margins -->
    <RelativeLayout
        android:id="@+id/outside"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/controls"
            style="?buttonBarStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginLeft="24dp"
            android:layout_marginTop="60dp"
            android:layout_marginRight="24dp"
            android:layout_marginBottom="60dp"
            android:background="#bf000000"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/controls_title_group"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <!-- These two are only used for audio -->
                <TextView
                    android:id="@+id/titleTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/tint_normal"
                    android:textSize="24sp"
                    android:visibility="gone"
                    tools:text="Song Title" />

                <TextView
                    android:id="@+id/minorTitleTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/tint_normal"
                    android:textSize="14sp"
                    android:visibility="gone"
                    tools:text="Artist / Album" />

                <!-- This one for video title display -->
                <TextView
                    android:id="@+id/fullTitleTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="middle"
                    android:gravity="center"
                    android:singleLine="true"
                    android:textColor="@color/tint_normal"
                    android:textSize="16sp"
                    tools:text="Video Title" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/controls_seekbar_group"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:weightSum="100">

                <ImageButton
                    android:id="@+id/prevBtn"
                    style="?buttonBarButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="48dp"
                    android:minHeight="48dp"
                    app:srcCompat="@drawable/ic_skip_previous_black_24dp"
                    app:tint="@color/tint_normal" />

                <TextView
                    android:id="@+id/playbackPositionTxt"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:padding="8dp"
                    android:textColor="@android:color/white"
                    tools:text="00:00" />

                <SeekBar
                    android:id="@+id/playbackSeekbar"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="100"
                    android:minHeight="24dp"
                    android:progressBackgroundTint="@color/tint_seekbar_bg" />

                <TextView
                    android:id="@+id/playbackDurationTxt"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:padding="8dp"
                    android:textColor="@android:color/white"
                    tools:text="13:37" />

                <ImageButton
                    android:id="@+id/nextBtn"
                    style="?buttonBarButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:minWidth="48dp"
                    android:minHeight="48dp"
                    app:srcCompat="@drawable/ic_skip_next_black_24dp"
                    app:tint="@color/tint_normal" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/controls_button_group"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/cycleAudioBtn"
                    style="?buttonBarButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:srcCompat="@drawable/ic_audiotrack_black_24dp"
                    app:tint="@color/tint_normal" />

                <ImageButton
                    android:id="@+id/cycleSubsBtn"
                    style="?buttonBarButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:srcCompat="@drawable/ic_subtitles_black_24dp"
                    app:tint="@color/tint_normal" />

                <ImageButton
                    android:id="@+id/playBtn"
                    style="?buttonBarButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@android:color/white"
                    app:tint="@color/tint_normal"
                    tools:src="@drawable/ic_play_arrow_black_24dp" />

                <Button
                    android:id="@+id/cycleDecoderBtn"
                    style="?buttonBarButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@android:color/white"
                    tools:text="HW+" />

                <Button
                    android:id="@+id/cycleSpeedBtn"
                    style="?buttonBarButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@android:color/white"
                    tools:text="1.00X" />

            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/statsTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_marginStart="16dp"
            android:shadowColor="#000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="1"
            android:textColor="#00FF00"
            android:visibility="gone"
            tools:text="60 FPS"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/gestureTextView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:shadowColor="#000000"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="4"
            android:textAlignment="center"
            android:textColor="#ffffff"
            android:textSize="36sp"
            android:textStyle="bold"
            android:visibility="gone"
            tools:text="[gesture]" />

        <LinearLayout
            android:id="@+id/top_controls"
            style="?buttonBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:alpha="0.5"
            android:background="#bf000000"
            android:orientation="horizontal">

            <ImageButton
                android:id="@+id/topLockBtn"
                style="?buttonBarButtonStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="40dp"
                android:minHeight="40dp"
                android:padding="4dp"
                app:srcCompat="@drawable/ic_lock_24dp"
                app:tint="@color/tint_normal" />

            <ImageButton
                android:id="@+id/topPiPBtn"
                style="?buttonBarButtonStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="40dp"
                android:minHeight="40dp"
                android:padding="4dp"
                app:srcCompat="@drawable/ic_picture_in_picture_24dp"
                app:tint="@color/tint_normal" />

            <ImageButton
                android:id="@+id/topMenuBtn"
                style="?buttonBarButtonStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="40dp"
                android:minHeight="40dp"
                android:padding="4dp"
                app:srcCompat="@drawable/ic_settings_black_24dp"
                app:tint="@color/tint_normal" />

        </LinearLayout>

        <!-- mismatching width/height so that the button appears exactly square?! -->
        <ImageButton
            android:id="@+id/unlockBtn"
            android:layout_width="48dp"
            android:layout_height="52dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="24dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="24dp"
            android:visibility="gone"
            app:srcCompat="@drawable/ic_lock_open_24dp" />

    </RelativeLayout>

</RelativeLayout>
