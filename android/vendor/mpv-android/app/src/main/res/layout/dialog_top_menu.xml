<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Since this dialog does not scroll, don't add too many buttons here! -->

    <TableRow
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <Button
            android:id="@+id/audioBtn"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/open_external_audio"
            android:textAllCaps="false" />

        <Button
            android:id="@+id/subBtn"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/open_external_sub"
            android:textAllCaps="false" />
    </TableRow>

    <Button
        android:id="@+id/playlistBtn"
        style="@style/Widget.AppCompat.Button.Borderless"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/action_playlist"
        android:textAllCaps="false" />

    <Button
        android:id="@+id/backgroundBtn"
        style="@style/Widget.AppCompat.Button.Borderless"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/resume_bg_playback"
        android:textAllCaps="false" />

    <TableRow
        android:id="@+id/rowChapter"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <Button
            android:id="@+id/chapterBtn"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="4"
            android:text="@string/chapter_button"
            android:textAllCaps="false" />

        <Button
            android:id="@+id/chapterPrev"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/alpha_darken"
            android:text="@string/dialog_prev"
            android:textAllCaps="false" />

        <Button
            android:id="@+id/chapterNext"
            style="@style/Widget.AppCompat.Button.Borderless"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/alpha_darken"
            android:text="@string/dialog_next"
            android:textAllCaps="false" />

    </TableRow>

    <Button
        android:id="@+id/advancedBtn"
        style="@style/Widget.AppCompat.Button.Borderless"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/advanced_menu"
        android:textAllCaps="false" />

    <Button
        android:id="@+id/orientationBtn"
        style="@style/Widget.AppCompat.Button.Borderless"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/switch_orientation"
        android:textAllCaps="false" />

</LinearLayout>
