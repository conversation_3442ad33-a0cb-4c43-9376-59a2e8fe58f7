<?xml version="1.0" encoding="utf-8"?>

<!--
  ~ This Source Code Form is subject to the terms of the Mozilla Public
  ~ License, v. 2.0. If a copy of the MPL was not distributed with this
  ~ file, You can obtain one at http://mozilla.org/MPL/2.0/.
  -->

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!--
      We configure the recycler to not clip to padding as we want it to
      draw into the padded area behind the navigation bar.
     -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@android:id/list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:descendantFocusability="afterDescendants"
        android:focusable="true"
        android:clipToPadding="false"
        android:fitsSystemWindows="true"
        tools:listitem="@layout/nnf_filepicker_listitem_dir"/>

    <FrameLayout
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?nnf_separator_color"/>

</RelativeLayout>
