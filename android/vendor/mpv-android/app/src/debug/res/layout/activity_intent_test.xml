<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="HardcodedText"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp">

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/switch1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/row1"
        android:text="subs data uri" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/switch2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/switch1"
        android:text="no hwdec" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/switch3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/switch2"
        android:text="force title" />

    <Button
        android:id="@+id/button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/row2"
        android:text="Launch" />

    <TextView
        android:id="@+id/info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/button"
        android:fontFamily="monospace"
        android:scrollbars="vertical"
        android:scrollHorizontally="true" />

    <LinearLayout
        android:id="@+id/row2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/switch3"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:labelFor="@id/editText1"
            android:text="offset:" />

        <SeekBar
            android:id="@+id/seekBar2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:max="30"
            android:progress="0" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/row1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:labelFor="@id/editText1"
            android:text="URL:" />

        <EditText
            android:id="@+id/editText1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:autofillHints=""
            android:inputType="textUri" />

    </LinearLayout>

</RelativeLayout>
