<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:ignore="HardcodedText">

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/switch1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:checked="true"
        android:padding="16dp"
        android:text="video only" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/switch2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@+id/switch1"
        android:checked="true"
        android:padding="16dp"
        android:text="decoders only" />

    <TextView
        android:id="@+id/info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/switch1"
        android:fontFamily="monospace"
        android:scrollbars="vertical"
        android:scrollHorizontally="true"
        android:textIsSelectable="true" />

</RelativeLayout>
