name: build

on:
  - push
  - pull_request

jobs:
  linux:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: temurin

      - name: Export env vars
        run: buildscripts/include/ci.sh export >>$GITHUB_ENV

      - uses: actions/cache@v4
        with:
          path: gh-cache/
          key: "${{ env.CACHE_IDENTIFIER }}"
          enableCrossOsArchive: true

      - name: Install deps
        run: |
          sudo apt-get update
          sudo apt-get install autoconf pkg-config libtool ninja-build python3-pip
          sudo pip3 install meson

      - name: Download deps
        run: |
          mkdir -p "$CACHE_FOLDER"
          buildscripts/include/ci.sh install
        env:
          CACHE_MODE: "folder"
          CACHE_FOLDER: "${{ github.workspace }}/gh-cache"

      - name: Build
        run: |
          buildscripts/include/ci.sh build

      - uses: actions/upload-artifact@v4
        with:
          name: mpv-android
          path: app/build/outputs/apk/default/debug/app-default-armeabi-v7a-debug.apk
