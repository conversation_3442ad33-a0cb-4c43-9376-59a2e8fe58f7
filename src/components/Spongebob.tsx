import React, { useState } from "react"
import { View, Text, Button, Image, Pressable } from "react-native"
import { Media } from "../types/plex"

export default function Spongebob() {
    const [media, setMedia] = useState<Media | null>(null)
    const [loading, setLoading] = useState(false)

    const fetchSpongebob = async () => {
        setLoading(true)
        console.log("Fetching from Go backend....")

        try {
            const response = await fetch("http://10.0.2.2:7070/media")
            const media: Media[] = await response.json()
            setMedia(media[0])
        } catch (error) {
            console.error("Error fetching from Go backend", error)
        } finally {
            setLoading(false)
        }
    }

    const playSpongebob = () => {
        console.log("Spongebobing....")
    }

    return (
        <View>
            <Button onPress={fetchSpongebob} title="Connect to Plex" accessibilityLabel="Click me to fetch data from plex"></Button>
            {loading && <Text>Loading...</Text>}

            {media && (
                <View className="w-[75%] self-center mt-10 flex gap-2">
                    <Pressable onPress={playSpongebob} className="relative w-44 self-center">
                        <Image className="h-72" resizeMode="contain" source={{ uri: media.poster }} />

                        <Image
                            className="w-14 h-14 absolute top-[50%] left-[50%] -translate-x-1/2 -translate-y-1/2 bg-white rounded-full"
                            source={{ uri: "https://cdn-icons-png.flaticon.com/512/109/109197.png" }}
                        />
                    </Pressable>

                    <Text className="text-3xl text-center">{media.title}</Text>

                    <Text className="text-center">{media.summary}</Text>
                </View>
            )}
        </View>
    )
}
