package main

import (
	"backend/internal/plex"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
)

type MediaItem struct {
	Title     string `json:"title"`
	Poster    string `json:"poster"`
	Summary   string `json:"summary"`
	StreamURL string `json:"streamUrl"`
}


var baseUrl string = "http://192.168.1.120:32400"
var token string = "PumzysdEksw2PphsCss4"


// CORS middleware to handle Cross-Origin Resource Sharing
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers for all responses
		w.<PERSON>er().Set("Access-Control-Allow-Origin", "*") // Allow requests from any origin
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		// Handle preflight OPTIONS requests
		if r.Method == "OPTIONS" {
			w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusOK)
			return
		}

		// Call the next handler
		next.ServeHTTP(w, r)
	})
}

func main() {
	// Create a new ServeMux
	mux := http.NewServeMux()

	// Register handlers
	mux.HandleFunc("/media", mediaHandler)

	// Create a server with CORS middleware
	handler := corsMiddleware(mux)

	port := 7070
	log.Printf("Backend running on port %d\n", port)
	log.Fatal(http.ListenAndServe(":"+strconv.Itoa(port), handler))
}

func mediaHandler(w http.ResponseWriter, r *http.Request) {
	log.Println("Received request!")

	// URL mode options:
	// - "test": Use a known working test URL
	// - "direct": Use direct file URL from Plex
	// - "transcode": Use transcoding URL (default)
	urlMode := r.URL.Query().Get("mode")
	if urlMode == "" {
		urlMode = "transcode" // Default mode
	}

	log.Printf("Using URL mode: %s", urlMode)

	client := plex.NewPlexClient(baseUrl, token)

	spongebobRaw, err := client.FetchSpongebob()
	if err != nil {
		fmt.Printf("Spongebob wasn't fetched :( : %v\n", err)
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	var spongebobs []MediaItem

	// Get the direct file path from the response
	directPath := spongebobRaw.MediaContainer.Metadata[0].Media[0].Part[0].Key

	var streamURL string

	switch urlMode {
	case "test":
		// Use a known working test URL for debugging
		streamURL = "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
		fmt.Println("Using test URL instead of Plex URL")

	case "direct":
		// Use direct file URL from Plex (original approach)
		streamURL = baseUrl + directPath + "?X-Plex-Token=" + token
		fmt.Println("Using direct file URL")

	case "transcode":
		// Construct a proper Plex streaming URL with transcoding parameters
		// This uses Plex's transcoding endpoint which is more compatible with mobile devices
		streamURL = fmt.Sprintf("%s/video/:/transcode/universal/start.m3u8?path=%s&mediaIndex=0&partIndex=0&protocol=hls&fastSeek=1&directPlay=0&directStream=1&X-Plex-Token=%s",
			baseUrl,
			directPath,
			token)
		fmt.Println("Using transcoding URL")

	default:
		// Fallback to a known working URL if mode is unrecognized
		streamURL = "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
		fmt.Println("Unrecognized mode, using test URL")
	}

	spongebob := MediaItem{
		Title:     spongebobRaw.MediaContainer.Metadata[0].Title,
		Poster:    baseUrl + spongebobRaw.MediaContainer.Metadata[0].Image[0].URL + "?X-Plex-Token=" + token,
		Summary:   spongebobRaw.MediaContainer.Metadata[0].Summary,
		StreamURL: streamURL,
	}

	// Log the streaming URL for debugging
	fmt.Println("Generated streaming URL:", streamURL)

	// Log the full media item for debugging
	b, err := json.MarshalIndent(spongebob, "", "  ")
	if err != nil {
		fmt.Println("Error marshalling:", err)
	} else {
		fmt.Println("Media item:", string(b))
	}

	spongebobs = append(spongebobs, spongebob)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(spongebobs)
}